import { getEnvironmentVariable } from "@langchain/core/utils/env";
import { Embeddings } from "@langchain/core/embeddings";
import { chunkArray } from "@langchain/core/utils/chunk_array";
/**
 * A class for generating embeddings using the Fireworks AI API.
 */
export class FireworksEmbeddings extends Embeddings {
    /**
     * Constructor for the FireworksEmbeddings class.
     * @param fields - An optional object with properties to configure the instance.
     */
    constructor(fields) {
        const fieldsWithDefaults = { ...fields };
        super(fieldsWithDefaults);
        /**
         * @deprecated Use `model` instead.
         */
        Object.defineProperty(this, "modelName", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: "nomic-ai/nomic-embed-text-v1.5"
        });
        Object.defineProperty(this, "model", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: "nomic-ai/nomic-embed-text-v1.5"
        });
        Object.defineProperty(this, "batchSize", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: 8
        });
        Object.defineProperty(this, "apiKey", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "basePath", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: "https://api.fireworks.ai/inference/v1"
        });
        Object.defineProperty(this, "apiUrl", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "headers", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        const apiKey = fieldsWithDefaults?.apiKey || getEnvironmentVariable("FIREWORKS_API_KEY");
        if (!apiKey) {
            throw new Error("Fireworks AI API key not found");
        }
        this.model = fieldsWithDefaults?.model ?? this.model;
        this.modelName = this.model;
        this.batchSize = fieldsWithDefaults?.batchSize ?? this.batchSize;
        this.apiKey = apiKey;
        this.apiUrl = `${this.basePath}/embeddings`;
    }
    /**
     * Generates embeddings for an array of texts.
     * @param texts - An array of strings to generate embeddings for.
     * @returns A Promise that resolves to an array of embeddings.
     */
    async embedDocuments(texts) {
        const batches = chunkArray(texts, this.batchSize);
        const batchRequests = batches.map((batch) => this.embeddingWithRetry({
            model: this.model,
            input: batch,
        }));
        const batchResponses = await Promise.all(batchRequests);
        const embeddings = [];
        for (let i = 0; i < batchResponses.length; i += 1) {
            const batch = batches[i];
            const { data: batchResponse } = batchResponses[i];
            for (let j = 0; j < batch.length; j += 1) {
                embeddings.push(batchResponse[j].embedding);
            }
        }
        return embeddings;
    }
    /**
     * Generates an embedding for a single text.
     * @param text - A string to generate an embedding for.
     * @returns A Promise that resolves to an array of numbers representing the embedding.
     */
    async embedQuery(text) {
        const { data } = await this.embeddingWithRetry({
            model: this.model,
            input: text,
        });
        return data[0].embedding;
    }
    /**
     * Makes a request to the Fireworks AI API to generate embeddings for an array of texts.
     * @param request - An object with properties to configure the request.
     * @returns A Promise that resolves to the response from the Fireworks AI API.
     */
    async embeddingWithRetry(request) {
        const makeCompletionRequest = async () => {
            const url = `${this.apiUrl}`;
            const response = await fetch(url, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${this.apiKey}`,
                    ...this.headers,
                },
                body: JSON.stringify(request),
            });
            if (!response.ok) {
                const { error: message } = await response.json();
                const error = new Error(`Error ${response.status}: ${message ?? "Unspecified error"}`);
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                error.response = response;
                throw error;
            }
            const json = await response.json();
            return json;
        };
        return this.caller.call(makeCompletionRequest);
    }
}
