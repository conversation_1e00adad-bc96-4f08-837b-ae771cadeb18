// Auto-generated by `scripts/create-entrypoints.js`. Do not edit manually.
export const optionalImportEntrypoints = [
    "langchain/agents/load",
    "langchain/agents/toolkits/sql",
    "langchain/tools/sql",
    "langchain/tools/webbrowser",
    "langchain/chains/load",
    "langchain/chains/query_constructor",
    "langchain/chains/query_constructor/ir",
    "langchain/chains/sql_db",
    "langchain/chains/graph_qa/cypher",
    "langchain/chat_models/universal",
    "langchain/document_loaders/web/apify_dataset",
    "langchain/document_loaders/web/assemblyai",
    "langchain/document_loaders/web/azure_blob_storage_container",
    "langchain/document_loaders/web/azure_blob_storage_file",
    "langchain/document_loaders/web/browserbase",
    "langchain/document_loaders/web/cheerio",
    "langchain/document_loaders/web/puppeteer",
    "langchain/document_loaders/web/playwright",
    "langchain/document_loaders/web/college_confidential",
    "langchain/document_loaders/web/gitbook",
    "langchain/document_loaders/web/hn",
    "langchain/document_loaders/web/imsdb",
    "langchain/document_loaders/web/figma",
    "langchain/document_loaders/web/firecrawl",
    "langchain/document_loaders/web/github",
    "langchain/document_loaders/web/notiondb",
    "langchain/document_loaders/web/notionapi",
    "langchain/document_loaders/web/pdf",
    "langchain/document_loaders/web/recursive_url",
    "langchain/document_loaders/web/s3",
    "langchain/document_loaders/web/sitemap",
    "langchain/document_loaders/web/sonix_audio",
    "langchain/document_loaders/web/confluence",
    "langchain/document_loaders/web/couchbase",
    "langchain/document_loaders/web/youtube",
    "langchain/document_loaders/fs/directory",
    "langchain/document_loaders/fs/multi_file",
    "langchain/document_loaders/fs/buffer",
    "langchain/document_loaders/fs/chatgpt",
    "langchain/document_loaders/fs/text",
    "langchain/document_loaders/fs/json",
    "langchain/document_loaders/fs/srt",
    "langchain/document_loaders/fs/pdf",
    "langchain/document_loaders/fs/docx",
    "langchain/document_loaders/fs/epub",
    "langchain/document_loaders/fs/csv",
    "langchain/document_loaders/fs/notion",
    "langchain/document_loaders/fs/obsidian",
    "langchain/document_loaders/fs/unstructured",
    "langchain/document_loaders/fs/openai_whisper_audio",
    "langchain/document_loaders/fs/pptx",
    "langchain/sql_db",
    "langchain/output_parsers/expression",
    "langchain/retrievers/self_query",
    "langchain/retrievers/self_query/functional",
    "langchain/cache/file_system",
    "langchain/stores/file/node",
    "langchain/storage/file_system",
    "langchain/hub",
    "langchain/experimental/prompts/handlebars",
    "langchain/experimental/tools/pyinterpreter",
];
