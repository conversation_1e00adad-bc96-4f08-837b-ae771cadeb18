import snakeCase from "decamelize";
import camelCase from "camelcase";
export function keyTo<PERSON><PERSON>(key, map) {
    return map?.[key] || snakeCase(key);
}
export function keyFrom<PERSON>son(key, map) {
    return map?.[key] || camelCase(key);
}
export function mapKeys(fields, mapper, map) {
    const mapped = {};
    for (const key in fields) {
        if (Object.hasOwn(fields, key)) {
            mapped[mapper(key, map)] = fields[key];
        }
    }
    return mapped;
}
