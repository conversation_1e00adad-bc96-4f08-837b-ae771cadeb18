"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SaveableVectorStore = exports.VectorStore = exports.VectorStoreRetriever = void 0;
const index_js_1 = require("./retrievers/index.cjs");
const serializable_js_1 = require("./load/serializable.cjs");
/**
 * Class for performing document retrieval from a VectorStore. Can perform
 * similarity search or maximal marginal relevance search.
 */
class VectorStoreRetriever extends index_js_1.BaseRetriever {
    static lc_name() {
        return "VectorStoreRetriever";
    }
    get lc_namespace() {
        return ["langchain_core", "vectorstores"];
    }
    _vectorstoreType() {
        return this.vectorStore._vectorstoreType();
    }
    constructor(fields) {
        super(fields);
        Object.defineProperty(this, "vectorStore", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "k", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: 4
        });
        Object.defineProperty(this, "searchType", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: "similarity"
        });
        Object.defineProperty(this, "searchKwargs", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "filter", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this.vectorStore = fields.vectorStore;
        this.k = fields.k ?? this.k;
        this.searchType = fields.searchType ?? this.searchType;
        this.filter = fields.filter;
        if (fields.searchType === "mmr") {
            this.searchKwargs = fields.searchKwargs;
        }
    }
    async _getRelevantDocuments(query, runManager) {
        if (this.searchType === "mmr") {
            if (typeof this.vectorStore.maxMarginalRelevanceSearch !== "function") {
                throw new Error(`The vector store backing this retriever, ${this._vectorstoreType()} does not support max marginal relevance search.`);
            }
            return this.vectorStore.maxMarginalRelevanceSearch(query, {
                k: this.k,
                filter: this.filter,
                ...this.searchKwargs,
            }, runManager?.getChild("vectorstore"));
        }
        return this.vectorStore.similaritySearch(query, this.k, this.filter, runManager?.getChild("vectorstore"));
    }
    async addDocuments(documents, options) {
        return this.vectorStore.addDocuments(documents, options);
    }
}
exports.VectorStoreRetriever = VectorStoreRetriever;
/**
 * Abstract class representing a store of vectors. Provides methods for
 * adding vectors and documents, deleting from the store, and searching
 * the store.
 */
class VectorStore extends serializable_js_1.Serializable {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    constructor(embeddings, dbConfig) {
        super(dbConfig);
        // Only ever instantiated in main LangChain
        Object.defineProperty(this, "lc_namespace", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: ["langchain", "vectorstores", this._vectorstoreType()]
        });
        Object.defineProperty(this, "embeddings", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this.embeddings = embeddings;
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    async delete(_params) {
        throw new Error("Not implemented.");
    }
    async similaritySearch(query, k = 4, filter = undefined, _callbacks = undefined // implement passing to embedQuery later
    ) {
        const results = await this.similaritySearchVectorWithScore(await this.embeddings.embedQuery(query), k, filter);
        return results.map((result) => result[0]);
    }
    async similaritySearchWithScore(query, k = 4, filter = undefined, _callbacks = undefined // implement passing to embedQuery later
    ) {
        return this.similaritySearchVectorWithScore(await this.embeddings.embedQuery(query), k, filter);
    }
    static fromTexts(_texts, _metadatas, _embeddings, 
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    _dbConfig) {
        throw new Error("the Langchain vectorstore implementation you are using forgot to override this, please report a bug");
    }
    static fromDocuments(_docs, _embeddings, 
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    _dbConfig) {
        throw new Error("the Langchain vectorstore implementation you are using forgot to override this, please report a bug");
    }
    asRetriever(kOrFields, filter, callbacks, tags, metadata, verbose) {
        if (typeof kOrFields === "number") {
            return new VectorStoreRetriever({
                vectorStore: this,
                k: kOrFields,
                filter,
                tags: [...(tags ?? []), this._vectorstoreType()],
                metadata,
                verbose,
                callbacks,
            });
        }
        else {
            const params = {
                vectorStore: this,
                k: kOrFields?.k,
                filter: kOrFields?.filter,
                tags: [...(kOrFields?.tags ?? []), this._vectorstoreType()],
                metadata: kOrFields?.metadata,
                verbose: kOrFields?.verbose,
                callbacks: kOrFields?.callbacks,
                searchType: kOrFields?.searchType,
            };
            if (kOrFields?.searchType === "mmr") {
                return new VectorStoreRetriever({
                    ...params,
                    searchKwargs: kOrFields.searchKwargs,
                });
            }
            return new VectorStoreRetriever({ ...params });
        }
    }
}
exports.VectorStore = VectorStore;
/**
 * Abstract class extending VectorStore with functionality for saving and
 * loading the vector store.
 */
class SaveableVectorStore extends VectorStore {
    static load(_directory, _embeddings) {
        throw new Error("Not implemented");
    }
}
exports.SaveableVectorStore = SaveableVectorStore;
