export declare function isPromiseMethod(x: string | symbol): x is "then" | "catch" | "finally";
export declare function isKVMap(x: unknown): x is Record<string, unknown>;
export declare const isAsyncIterable: (x: unknown) => x is AsyncIterable<unknown>;
export declare const isIteratorLike: (x: unknown) => x is Iterator<unknown, any, undefined>;
export declare const isGenerator: (x: unknown) => x is Generator<unknown, any, unknown>;
export declare const isThenable: (x: unknown) => x is Promise<unknown>;
export declare const isReadableStream: (x: unknown) => x is ReadableStream<any>;
