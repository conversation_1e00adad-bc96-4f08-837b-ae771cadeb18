"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Validator = exports.deepCompareStrict = void 0;
var index_js_1 = require("./@cfworker/json-schema/index.cjs");
Object.defineProperty(exports, "deepCompareStrict", { enumerable: true, get: function () { return index_js_1.deepCompareStrict; } });
Object.defineProperty(exports, "Validator", { enumerable: true, get: function () { return index_js_1.Validator; } });
